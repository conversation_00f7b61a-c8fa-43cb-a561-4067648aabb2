@echo off
echo ==================================
echo 智能造数工具启动脚本
echo ==================================

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js已安装
node --version

REM 安装前端依赖
echo 正在安装前端依赖...
if exist frontend-package.json (
    copy frontend-package.json package.json
)

call npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)

REM 安装后端依赖
echo 正在安装后端依赖...
if exist backend-requirements.txt (
    pip install -r backend-requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖安装完成

REM 启动后端
echo 正在启动后端服务...
start "Backend" python app.py

REM 等待后端启动
timeout /t 3 /nobreak >nul

REM 启动前端
echo 正在启动前端服务...
start "Frontend" npm run dev

echo ==================================
echo 🎉 服务启动成功!
echo 前端地址: http://localhost:3000
echo 后端地址: http://localhost:8000
echo 关闭此窗口将停止服务
echo ==================================

pause

<template>
  <div class="container mx-auto p-8">
    <h1 class="text-3xl font-bold mb-8 text-center">智能造数工具</h1>

    <div class="bg-white rounded-lg shadow-xl p-8 mb-8">
      <div class="mb-6">
        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">内容描述</label>
        <textarea
          id="description"
          v-model="description"
          rows="4"
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="请描述您想要生成的数据内容..."
        ></textarea>
      </div>

      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">上传Excel文件</label>
        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
          <div class="space-y-1 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <div class="flex text-sm text-gray-600">
              <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none">
                <span>上传文件</span>
                <input id="file-upload" name="file-upload" type="file" class="sr-only" @change="handleFileUpload">
              </label>
              <p class="pl-1">或拖放文件</p>
            </div>
            <p class="text-xs text-gray-500">
              支持XLSX格式文件
            </p>
          </div>
        </div>
        <div v-if="fileName" class="mt-2 text-sm text-gray-700">
          <span class="font-medium">已选择文件:</span> {{ fileName }}
        </div>
      </div>

      <button
        @click="submitForm"
        :disabled="!description || !selectedFile"
        class="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
      >
        开始处理
      </button>
    </div>

    <div v-if="processing" class="bg-white rounded-lg shadow-xl p-8 mb-8">
      <div class="flex items-center justify-center">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        <p class="ml-4 text-lg">正在处理数据，请稍候...</p>
      </div>
    </div>

    <div v-if="downloadUrl" class="bg-white rounded-lg shadow-xl p-8">
      <div class="text-center">
        <p class="text-lg mb-4">数据处理完成！</p>
        <a
          :href="downloadUrl"
          download="processed_data.xlsx"
          class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition duration-150 ease-in-out"
        >
          <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
          </svg>
          下载处理后的文件
        </a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      description: '',
      selectedFile: null,
      fileName: '',
      processing: false,
      downloadUrl: ''
    }
  },
  methods: {
    handleFileUpload(event) {
      const file = event.target.files[0];
      if (file) {
        this.selectedFile = file;
        this.fileName = file.name;
      }
    },
    async submitForm() {
      if (!this.description || !this.selectedFile) return;

      this.processing = true;
      this.downloadUrl = '';

      try {
        const formData = new FormData();
        formData.append('description', this.description);
        formData.append('file', this.selectedFile);

        const response = await fetch('http://localhost:8000/process/', {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          const blob = await response.blob();
          this.downloadUrl = URL.createObjectURL(blob);
        } else {
          const errorData = await response.json();
          alert(`处理失败: ${errorData.error}`);
        }
      } catch (error) {
        console.error('Error submitting form:', error);
        alert('发生错误，请重试');
      } finally {
        this.processing = false;
      }
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 800px;
}

.primary-500 {
  color: #3b82f6;
}

.primary-600 {
  color: #2563eb;
}

.primary-700 {
  color: #1d4ed8;
}
</style>
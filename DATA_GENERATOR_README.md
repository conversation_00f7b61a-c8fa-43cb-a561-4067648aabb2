# 智能造数工具

基于DeepSeek大模型的智能数据生成工具，支持Excel文件的智能填充和数据生成。

## 功能特性

- 📊 **Excel文件处理**: 支持上传Excel文件并智能填充数据
- 🤖 **AI驱动**: 集成DeepSeek大模型，根据用户描述生成相关内容
- 🔍 **网络搜索**: 结合网络搜索结果丰富数据内容
- 🎨 **现代界面**: 基于Vue 3和Tailwind CSS的现代化用户界面
- ⚡ **高性能**: FastAPI后端，响应速度快

## 技术栈

### 前端
- Vue 3 (Composition API)
- Tailwind CSS
- Vite

### 后端
- FastAPI
- Pandas
- Uvicorn

## 快速开始

### 方式一：一键启动（推荐）

```bash
# 运行启动脚本
python start.py
```

启动脚本会自动：
1. 检查运行环境
2. 安装前后端依赖
3. 启动前后端服务

### 方式二：手动启动

#### 环境要求
- Python 3.8+
- Node.js 16+
- npm 或 yarn

#### 1. 安装后端依赖
```bash
# 安装Python依赖
pip install fastapi uvicorn pandas openpyxl python-multipart
```

#### 2. 安装前端依赖
```bash
# 重命名前端配置文件
mv frontend-package.json package.json

# 安装前端依赖
npm install
```

#### 3. 启动后端服务
```bash
# 启动FastAPI服务
python app.py
```
后端服务将在 http://localhost:8000 启动

#### 4. 启动前端服务
```bash
# 启动前端开发服务器
npm run dev
```
前端服务将在 http://localhost:3000 启动

## 使用说明

1. **访问应用**: 打开浏览器访问 http://localhost:3000

2. **输入描述**: 在"内容描述"文本框中输入您想要生成的数据类型和要求

3. **上传文件**: 点击上传区域选择Excel文件（支持.xlsx格式）

4. **开始处理**: 点击"开始处理"按钮，系统将：
   - 分析Excel文件的列结构
   - 根据您的描述调用DeepSeek API生成内容
   - 结合网络搜索丰富数据
   - 填充Excel文件

5. **下载结果**: 处理完成后，点击"下载处理后的文件"获取结果

## 项目结构

```
.
├── app.py                    # FastAPI后端主文件
├── src/
│   ├── App.vue              # Vue前端主组件
│   └── main.js              # Vue应用入口
├── index.html               # HTML入口文件
├── vite.config.js           # Vite配置文件
├── start.py                 # 一键启动脚本
├── frontend-package.json    # 前端依赖配置
├── backend-requirements.txt # 后端依赖配置
└── README.md               # 项目说明
```

## API接口

### POST /process/
处理Excel文件并生成数据

**请求参数:**
- `description` (form): 数据描述文本
- `file` (file): Excel文件

**响应:**
- 成功: 返回处理后的Excel文件
- 失败: 返回错误信息JSON

## 开发说明

### 自定义DeepSeek API
当前使用模拟的DeepSeek API调用，实际使用时需要：

1. 在 `app.py` 中的 `call_deepseek_api` 函数中集成真实的DeepSeek API
2. 配置API密钥和端点
3. 处理API响应格式

### 自定义网络搜索
当前使用模拟的网络搜索，实际使用时可以集成：
- Google Search API
- Bing Search API
- 其他搜索服务

## 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 检查端口占用
   lsof -i :3000  # 前端端口
   lsof -i :8000  # 后端端口
   ```

2. **依赖安装失败**
   ```bash
   # 清理npm缓存
   npm cache clean --force
   
   # 使用国内镜像
   npm install --registry https://registry.npmmirror.com
   ```

3. **Python依赖问题**
   ```bash
   # 使用虚拟环境
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # 或
   venv\Scripts\activate     # Windows
   ```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

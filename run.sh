#!/bin/bash

echo "=================================="
echo "智能造数工具启动脚本"
echo "=================================="

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js"
    echo "下载地址: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js已安装: $(node --version)"

# 安装前端依赖
echo "正在安装前端依赖..."
if [ -f "frontend-package.json" ]; then
    cp frontend-package.json package.json
fi

npm install
if [ $? -ne 0 ]; then
    echo "❌ 前端依赖安装失败"
    exit 1
fi

# 安装后端依赖
echo "正在安装后端依赖..."
if [ -f "backend-requirements.txt" ]; then
    pip install -r backend-requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ 后端依赖安装失败"
        exit 1
    fi
fi

echo "✅ 依赖安装完成"

# 启动后端
echo "正在启动后端服务..."
python app.py &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 启动前端
echo "正在启动前端服务..."
npm run dev &
FRONTEND_PID=$!

echo "=================================="
echo "🎉 服务启动成功!"
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:8000"
echo "按 Ctrl+C 停止服务"
echo "=================================="

# 等待用户中断
trap "echo '正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait

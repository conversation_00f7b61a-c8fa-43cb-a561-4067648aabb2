#!/usr/bin/env python3
"""
智能造数工具启动脚本
同时启动前端和后端服务
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

def check_node_installed():
    """检查Node.js是否已安装"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Node.js已安装: {result.stdout.strip()}")
            return True
        else:
            print("✗ Node.js未安装")
            return False
    except FileNotFoundError:
        print("✗ Node.js未安装")
        return False

def check_python_packages():
    """检查Python依赖包是否已安装"""
    try:
        import fastapi
        import uvicorn
        import pandas
        print("✓ Python依赖包已安装")
        return True
    except ImportError as e:
        print(f"✗ Python依赖包缺失: {e}")
        return False

def install_frontend_dependencies():
    """安装前端依赖"""
    print("正在安装前端依赖...")
    
    # 重命名package.json文件
    if os.path.exists('frontend-package.json'):
        if os.path.exists('package.json'):
            os.remove('package.json')
        os.rename('frontend-package.json', 'package.json')
    
    try:
        subprocess.run(['npm', 'install'], check=True)
        print("✓ 前端依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("✗ 前端依赖安装失败")
        return False
    except FileNotFoundError:
        print("✗ npm命令未找到，请先安装Node.js")
        return False

def install_backend_dependencies():
    """安装后端依赖"""
    print("正在安装后端依赖...")
    
    # 重命名requirements.txt文件
    if os.path.exists('backend-requirements.txt'):
        if os.path.exists('requirements.txt'):
            # 备份原有的requirements.txt
            os.rename('requirements.txt', 'requirements.txt.backup')
        os.rename('backend-requirements.txt', 'requirements.txt')
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], check=True)
        print("✓ 后端依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("✗ 后端依赖安装失败")
        return False

def start_backend():
    """启动后端服务"""
    print("正在启动后端服务...")
    try:
        process = subprocess.Popen([sys.executable, 'app.py'])
        print("✓ 后端服务已启动 (http://localhost:8000)")
        return process
    except Exception as e:
        print(f"✗ 后端服务启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("正在启动前端服务...")
    try:
        process = subprocess.Popen(['npm', 'run', 'dev'])
        print("✓ 前端服务已启动 (http://localhost:3000)")
        return process
    except Exception as e:
        print(f"✗ 前端服务启动失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 50)
    print("智能造数工具启动器")
    print("=" * 50)
    
    # 检查环境
    print("\n1. 检查运行环境...")
    node_ok = check_node_installed()
    python_ok = check_python_packages()
    
    # 安装依赖
    print("\n2. 安装依赖...")
    if node_ok:
        frontend_ok = install_frontend_dependencies()
    else:
        frontend_ok = False
        print("请先安装Node.js: https://nodejs.org/")
    
    if python_ok:
        backend_ok = True
    else:
        backend_ok = install_backend_dependencies()
    
    if not (frontend_ok and backend_ok):
        print("\n依赖安装失败，请检查错误信息")
        return
    
    # 启动服务
    print("\n3. 启动服务...")
    backend_process = start_backend()
    time.sleep(3)  # 等待后端启动
    
    frontend_process = start_frontend()
    
    if not (backend_process and frontend_process):
        print("\n服务启动失败")
        return
    
    print("\n" + "=" * 50)
    print("🎉 服务启动成功!")
    print("前端地址: http://localhost:3000")
    print("后端地址: http://localhost:8000")
    print("按 Ctrl+C 停止服务")
    print("=" * 50)
    
    # 等待用户中断
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在停止服务...")
        if backend_process:
            backend_process.terminate()
        if frontend_process:
            frontend_process.terminate()
        print("服务已停止")

if __name__ == "__main__":
    main()
